<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Organic Paper Fold Animation</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(
          135deg,
          #0f0f23 0%,
          #1a1a2e 50%,
          #16213e 100%
        );
        min-height: 100vh;
        margin: 0;
        padding: 40px 20px;
        color: white;
        overflow-x: hidden;
      }

      .main-container {
        max-width: 1400px;
        margin: 0 auto;
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        grid-template-rows: auto 1fr auto;
        gap: 40px;
        min-height: calc(100vh - 80px);
        grid-template-areas:
          "header header header"
          "sidebar animation controls"
          "footer footer footer";
      }

      .header {
        grid-area: header;
        text-align: center;
        padding: 20px 0;
      }

      .animation-area {
        grid-area: animation;
        display: flex;
        align-items: center;
        justify-content: center;
        perspective: 1500px;
        perspective-origin: center center;
        min-height: 500px;
      }

      .paper-container {
        position: relative;
        width: 600px;
        height: 420px;
        transform-style: preserve-3d;
        transition: all 0.5s ease;
        max-width: 90vw;
        max-height: 70vh;
      }

      .paper-piece {
        position: absolute;
        background: #f8f8f8;
        border: 1px solid #ddd;
        transform-style: preserve-3d;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.2));
      }

      /* Header styling */
      .header h1 {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 10px 0;
        letter-spacing: -0.02em;
      }

      .header p {
        font-size: 1.2rem;
        opacity: 0.8;
        margin: 0;
        font-weight: 300;
      }

      /* Main image reveal area */
      .reveal-area {
        width: 360px;
        height: 240px;
        top: 90px;
        left: 120px;
        background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzYwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDM2MCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzNjAiIGhlaWdodD0iMjQwIiBmaWxsPSIjNGY0NmU1Ii8+Cjx0ZXh0IHg9IjE4MCIgeT0iMTIwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPllPVVIgSU1BR0U8L3RleHQ+CjwvU3ZnPgo=");
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 1;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transition: all 0.5s ease;
      }

      /* Paper wrinkle overlay that blends with the uploaded image */
      .paper-overlay {
        position: absolute;
        top: -20px;
        left: -20px;
        width: calc(100% + 40px);
        height: calc(100% + 40px);
        background: transparent;
        z-index: 2;
        transform-origin: center center;
        animation: reverseUncrumpling 8s cubic-bezier(0.25, 0.46, 0.45, 0.94)
          infinite;
        border-radius: 12px;
        transition: all 0.5s ease;
        mix-blend-mode: multiply;
        opacity: 0.8;
      }

      /* Wrinkle texture that creates paper creases and blends with image */
      .paper-overlay::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          /* Dynamic wrinkle patterns that animate */ radial-gradient(
            ellipse at 20% 30%,
            rgba(0, 0, 0, 0.15) 1px,
            transparent 4px
          ),
          radial-gradient(
            ellipse at 80% 70%,
            rgba(0, 0, 0, 0.12) 1px,
            transparent 3px
          ),
          radial-gradient(
            ellipse at 60% 20%,
            rgba(0, 0, 0, 0.1) 0.5px,
            transparent 2px
          ),
          radial-gradient(
            ellipse at 30% 80%,
            rgba(0, 0, 0, 0.13) 1px,
            transparent 3px
          ),
          radial-gradient(
            ellipse at 70% 50%,
            rgba(0, 0, 0, 0.08) 0.8px,
            transparent 2px
          ),
          /* Fold lines that create depth */
            linear-gradient(
              45deg,
              transparent 40%,
              rgba(0, 0, 0, 0.08) 50%,
              transparent 60%
            ),
          linear-gradient(
            -30deg,
            transparent 35%,
            rgba(0, 0, 0, 0.06) 45%,
            transparent 55%
          ),
          linear-gradient(
            120deg,
            transparent 30%,
            rgba(0, 0, 0, 0.07) 40%,
            transparent 50%
          ),
          /* Paper fiber texture */
            url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxkZWZzPgo8cGF0dGVybiBpZD0icGFwZXIiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCI+CjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjAyIi8+CjxjaXJjbGUgY3g9IjMiIGN5PSI3IiByPSIwLjUiIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4wMyIvPgo8Y2lyY2xlIGN4PSIxNSIgY3k9IjEzIiByPSIwLjMiIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4wMiIvPgo8L3BhdHRlcm4+CjwvZGVmcz4KPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXBlcikiLz4KPC9zdmc+");
        background-size: 30px 25px, 35px 30px, 20px 18px, 40px 32px, 25px 22px,
          60px 45px, 50px 40px, 45px 35px, 15px 15px;
        opacity: 1;
        pointer-events: none;
        border-radius: 12px;
        animation: wrinkleUnfolding 8s ease-in-out infinite;
        mix-blend-mode: multiply;
      }

      /* Organic wrinkles and folds that appear during animation */
      .paper-overlay::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            ellipse at 30% 20%,
            rgba(0, 0, 0, 0.08) 1px,
            transparent 3px
          ),
          radial-gradient(
            ellipse at 70% 80%,
            rgba(0, 0, 0, 0.06) 1px,
            transparent 2px
          ),
          radial-gradient(
            ellipse at 20% 70%,
            rgba(0, 0, 0, 0.05) 0.5px,
            transparent 2px
          ),
          linear-gradient(
            45deg,
            transparent 40%,
            rgba(0, 0, 0, 0.03) 50%,
            transparent 60%
          ),
          linear-gradient(
            -45deg,
            transparent 35%,
            rgba(0, 0, 0, 0.04) 45%,
            transparent 55%
          );
        background-size: 40px 30px, 35px 40px, 25px 20px, 60px 50px, 55px 45px;
        opacity: 0;
        animation: wrinkleAppear 4s ease-in-out infinite;
        pointer-events: none;
        border-radius: 8px;
      }

      /* Reverse uncrumpling animation - simulates paper being unfolded */
      @keyframes reverseUncrumpling {
        0%,
        8% {
          /* Fully crumpled state - maximum wrinkles and distortion */
          transform: scale(0.3) rotate(15deg) skew(5deg, 3deg);
          filter: blur(2px) brightness(0.7) contrast(1.3);
          opacity: 0.9;
        }

        15% {
          /* Starting to unfold - less crumpled */
          transform: scale(0.45) rotate(8deg) skew(3deg, 2deg);
          filter: blur(1.5px) brightness(0.75) contrast(1.25);
          opacity: 0.85;
        }

        25% {
          /* Partial unfolding - major creases smoothing out */
          transform: scale(0.65) rotate(4deg) skew(2deg, 1deg);
          filter: blur(1px) brightness(0.8) contrast(1.2);
          opacity: 0.8;
        }

        35% {
          /* More unfolding - wrinkles becoming less pronounced */
          transform: scale(0.8) rotate(2deg) skew(1deg, 0.5deg);
          filter: blur(0.8px) brightness(0.85) contrast(1.15);
          opacity: 0.75;
        }

        50% {
          /* Halfway unfolded - most major wrinkles gone */
          transform: scale(0.92) rotate(1deg) skew(0.5deg, 0.2deg);
          filter: blur(0.5px) brightness(0.9) contrast(1.1);
          opacity: 0.7;
        }

        65% {
          /* Nearly flat - only minor creases remain */
          transform: scale(0.98) rotate(0.5deg) skew(0.2deg, 0.1deg);
          filter: blur(0.3px) brightness(0.95) contrast(1.05);
          opacity: 0.6;
        }

        80% {
          /* Almost completely flat - very subtle wrinkles */
          transform: scale(1.02) rotate(0.2deg) skew(0.1deg, 0deg);
          filter: blur(0.1px) brightness(0.98) contrast(1.02);
          opacity: 0.4;
        }

        90% {
          /* Completely flat - no wrinkles, just texture */
          transform: scale(1.05) rotate(0deg) skew(0deg, 0deg);
          filter: blur(0px) brightness(1) contrast(1);
          opacity: 0.2;
        }

        95% {
          /* Settling - slight overshoot */
          transform: scale(1.02) rotate(0deg) skew(0deg, 0deg);
          filter: blur(0px) brightness(1) contrast(1);
          opacity: 0.1;
        }

        100% {
          /* Final state - paper is completely unfolded */
          transform: scale(1) rotate(0deg) skew(0deg, 0deg);
          filter: blur(0px) brightness(1) contrast(1);
          opacity: 0;
        }
      }

      /* Dynamic wrinkle animation that changes as paper unfolds */
      @keyframes wrinkleUnfolding {
        0%,
        10% {
          /* Maximum wrinkles - heavily crumpled */
          background-position: 0px 0px, 5px 3px, 2px 7px, 8px 1px, 3px 9px,
            12px 8px, 15px 11px, 9px 14px;
          opacity: 1;
          transform: scale(1.2);
        }

        20% {
          /* Wrinkles starting to smooth out */
          background-position: 2px 1px, 7px 2px, 4px 5px, 6px 3px, 5px 7px,
            10px 6px, 13px 9px, 7px 12px;
          opacity: 0.9;
          transform: scale(1.15);
        }

        35% {
          /* Major wrinkles reducing */
          background-position: 3px 2px, 8px 1px, 5px 4px, 4px 6px, 6px 5px,
            8px 4px, 11px 7px, 5px 10px;
          opacity: 0.8;
          transform: scale(1.1);
        }

        50% {
          /* Halfway unfolded - moderate wrinkles */
          background-position: 4px 3px, 6px 4px, 3px 6px, 7px 2px, 4px 8px,
            9px 5px, 12px 8px, 6px 11px;
          opacity: 0.6;
          transform: scale(1.05);
        }

        70% {
          /* Minor wrinkles remaining */
          background-position: 2px 4px, 5px 2px, 1px 5px, 3px 7px, 2px 6px,
            6px 3px, 8px 5px, 4px 8px;
          opacity: 0.4;
          transform: scale(1.02);
        }

        85% {
          /* Very subtle texture only */
          background-position: 1px 2px, 3px 1px, 2px 3px, 4px 2px, 1px 4px,
            3px 2px, 5px 3px, 2px 5px;
          opacity: 0.2;
          transform: scale(1.01);
        }

        100% {
          /* Completely smooth - no wrinkles */
          background-position: 0px 0px, 1px 0px, 0px 1px, 1px 1px, 0px 2px,
            1px 2px, 2px 1px, 1px 3px;
          opacity: 0;
          transform: scale(1);
        }
      }

      /* Single animation mode - one-time reverse uncrumpling */
      .paper-container.single-mode .paper-overlay {
        animation: singleReverseUncrumpling 5s
          cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
      }

      @keyframes singleReverseUncrumpling {
        0% {
          /* Fully crumpled state */
          transform: scale(0.3) rotate(15deg) skew(5deg, 3deg);
          filter: blur(2px) brightness(0.7) contrast(1.3);
          opacity: 0.9;
        }

        20% {
          /* Starting to unfold */
          transform: scale(0.5) rotate(10deg) skew(3deg, 2deg);
          filter: blur(1.5px) brightness(0.75) contrast(1.25);
          opacity: 0.8;
        }

        40% {
          /* Partial unfolding */
          transform: scale(0.7) rotate(5deg) skew(2deg, 1deg);
          filter: blur(1px) brightness(0.8) contrast(1.2);
          opacity: 0.7;
        }

        60% {
          /* More unfolding */
          transform: scale(0.85) rotate(2deg) skew(1deg, 0.5deg);
          filter: blur(0.6px) brightness(0.85) contrast(1.15);
          opacity: 0.5;
        }

        80% {
          /* Nearly flat */
          transform: scale(0.95) rotate(0.5deg) skew(0.3deg, 0.1deg);
          filter: blur(0.2px) brightness(0.92) contrast(1.08);
          opacity: 0.3;
        }

        95% {
          /* Almost completely flat */
          transform: scale(1.02) rotate(0.1deg) skew(0.1deg, 0deg);
          filter: blur(0.05px) brightness(0.98) contrast(1.02);
          opacity: 0.1;
        }

        100% {
          /* Completely unfolded */
          transform: scale(1) rotate(0deg) skew(0deg, 0deg);
          filter: blur(0px) brightness(1) contrast(1);
          opacity: 0;
        }
      }

      /* Texture upload section */
      .texture-upload {
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      .texture-upload h4 {
        color: #667eea;
        margin-bottom: 15px;
        font-size: 1.2rem;
        font-weight: 600;
      }

      .texture-btn {
        display: block;
        width: 100%;
        padding: 12px 20px;
        background: rgba(255, 255, 255, 0.08);
        border: 2px dashed rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-align: center;
        margin-bottom: 10px;
      }

      .texture-btn:hover {
        background: rgba(255, 255, 255, 0.12);
        border-color: rgba(255, 255, 255, 0.5);
      }

      .texture-description {
        font-size: 0.85rem;
        opacity: 0.6;
        line-height: 1.4;
        text-align: center;
      }

      /* Subtle texture animation for organic feel */
      @keyframes subtleTextureShift {
        0%,
        100% {
          background-position: 0px 0px;
        }
        25% {
          background-position: 2px 1px;
        }
        50% {
          background-position: 1px 3px;
        }
        75% {
          background-position: 3px 2px;
        }
      }

      /* Pause mode */
      .paper-container.paused .paper-overlay {
        animation-play-state: paused;
      }

      /* Sidebar with upload area */
      .sidebar {
        grid-area: sidebar;
        display: flex;
        flex-direction: column;
        gap: 30px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        height: fit-content;
      }

      .upload-section h3 {
        font-size: 1.4rem;
        margin-bottom: 15px;
        color: #667eea;
        font-weight: 600;
      }

      .file-input {
        display: none;
      }

      .upload-btn {
        display: block;
        width: 100%;
        padding: 16px 24px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 12px;
        cursor: pointer;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        margin-bottom: 15px;
        text-align: center;
      }

      .upload-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
      }

      .upload-description {
        font-size: 0.95rem;
        opacity: 0.7;
        line-height: 1.5;
        text-align: center;
      }

      /* Controls panel */
      .controls-panel {
        grid-area: controls;
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        height: fit-content;
      }

      .controls-panel h3 {
        font-size: 1.4rem;
        margin-bottom: 10px;
        color: #667eea;
        font-weight: 600;
      }

      .controls {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .btn {
        padding: 14px 20px;
        background: rgba(255, 255, 255, 0.08);
        border: 2px solid rgba(255, 255, 255, 0.15);
        color: white;
        border-radius: 10px;
        cursor: pointer;
        font-weight: 500;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        text-align: center;
      }

      .btn:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      .btn:active {
        transform: translateY(0);
      }

      /* Instructions within sidebar */
      .instructions {
        margin-top: 20px;
      }

      .instructions h4 {
        color: #667eea;
        margin-bottom: 15px;
        font-size: 1.2rem;
        font-weight: 600;
      }

      .instructions ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .instructions li {
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
        font-size: 0.9rem;
        opacity: 0.8;
        line-height: 1.4;
      }

      .instructions li::before {
        content: "→";
        position: absolute;
        left: 0;
        color: #667eea;
        font-weight: bold;
      }

      /* Footer */
      .footer {
        grid-area: footer;
        text-align: center;
        padding: 20px 0;
        opacity: 0.6;
        font-size: 0.9rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: 20px;
      }

      @media (max-width: 1200px) {
        .main-container {
          grid-template-columns: 1fr;
          grid-template-areas:
            "header"
            "animation"
            "sidebar"
            "controls"
            "footer";
          gap: 30px;
        }

        .sidebar,
        .controls-panel {
          max-width: 600px;
          margin: 0 auto;
        }

        .header h1 {
          font-size: 2.5rem;
        }
      }

      @media (max-width: 768px) {
        body {
          padding: 20px 15px;
        }

        .main-container {
          gap: 20px;
        }

        .paper-container {
          width: 480px;
          height: 336px;
        }

        .reveal-area {
          width: 288px;
          height: 192px;
          top: 72px;
          left: 96px;
        }

        .piece-1,
        .piece-2,
        .piece-3,
        .piece-4 {
          width: 144px;
          height: 96px;
        }

        .piece-1 {
          top: 72px;
          left: 96px;
        }
        .piece-2 {
          top: 72px;
          right: 96px;
        }
        .piece-3 {
          bottom: 72px;
          left: 96px;
        }
        .piece-4 {
          bottom: 72px;
          right: 96px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .header p {
          font-size: 1rem;
        }

        .sidebar,
        .controls-panel {
          padding: 20px;
        }
      }

      @media (max-width: 480px) {
        .paper-container {
          width: 360px;
          height: 252px;
        }

        .reveal-area {
          width: 216px;
          height: 144px;
          top: 54px;
          left: 72px;
        }

        .piece-1,
        .piece-2,
        .piece-3,
        .piece-4 {
          width: 108px;
          height: 72px;
        }

        .piece-1 {
          top: 54px;
          left: 72px;
        }
        .piece-2 {
          top: 54px;
          right: 72px;
        }
        .piece-3 {
          bottom: 54px;
          left: 72px;
        }
        .piece-4 {
          bottom: 54px;
          right: 72px;
        }

        .header h1 {
          font-size: 1.8rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="main-container">
      <!-- Header -->
      <div class="header">
        <h1>Organic Paper Fold Animation</h1>
        <p>Watch your images come to life through natural paper unfolding</p>
      </div>

      <!-- Sidebar with Upload -->
      <div class="sidebar">
        <div class="upload-section">
          <h3>📸 Upload Image</h3>
          <label for="imageUpload" class="upload-btn">
            Choose Your Image
          </label>
          <input
            type="file"
            id="imageUpload"
            class="file-input"
            accept="image/*"
          />
          <div class="upload-description">
            Upload an image to see it revealed through organic paper animation.
            Supports JPG, PNG, and other common formats.
          </div>
        </div>

        <div class="texture-upload">
          <h4>🎨 Paper Texture</h4>
          <label for="textureUpload" class="texture-btn">
            Upload Paper Texture
          </label>
          <input
            type="file"
            id="textureUpload"
            class="file-input"
            accept="image/*"
          />
          <div class="texture-description">
            Upload your own paper texture for a custom look. Works best with
            seamless patterns.
          </div>
        </div>

        <div class="instructions">
          <h4>How to Use:</h4>
          <ul>
            <li>Upload your image above</li>
            <li>Optionally add custom paper texture</li>
            <li>Watch organic paper unfold from center</li>
            <li>Use controls on the right</li>
            <li>Press SPACE to pause</li>
            <li>Press R to reset</li>
            <li>Press ENTER to trigger once</li>
          </ul>
        </div>
      </div>

      <!-- Animation Area -->
      <div class="animation-area">
        <div class="paper-container" id="paperContainer">
          <div class="paper-piece reveal-area" id="revealArea"></div>
          <div class="paper-overlay" id="paperOverlay"></div>
        </div>
      </div>

      <!-- Controls Panel -->
      <div class="controls-panel">
        <h3>🎮 Animation Controls</h3>
        <div class="controls">
          <button class="btn" onclick="triggerUnfold()">🎬 Unfold Once</button>
          <button class="btn" onclick="pauseAnimation()">⏸️ Pause</button>
          <button class="btn" onclick="resumeAnimation()">▶️ Resume</button>
          <button class="btn" onclick="resetAnimation()">🔄 Reset</button>
          <button class="btn" onclick="changeSpeed()">⚡ Speed: 1x</button>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <p>
          Drag and drop images anywhere on the page • Keyboard shortcuts
          available
        </p>
      </div>
    </div>

    <script>
      const paperContainer = document.getElementById("paperContainer");
      const revealArea = document.getElementById("revealArea");
      const paperOverlay = document.getElementById("paperOverlay");
      const imageUpload = document.getElementById("imageUpload");
      const textureUpload = document.getElementById("textureUpload");
      let isPaused = false;
      let currentSpeed = 1;
      const speeds = [0.5, 1, 1.5, 2];
      let speedIndex = 1;

      // Image upload functionality with dynamic sizing
      imageUpload.addEventListener("change", function (e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function (e) {
            const img = new Image();
            img.onload = function () {
              resizeAnimationToImage(img.width, img.height);
              revealArea.style.backgroundImage = `url(${e.target.result})`;
              revealArea.style.backgroundSize = "contain";
              revealArea.style.backgroundPosition = "center";
              revealArea.style.backgroundRepeat = "no-repeat";
              // Trigger a single unfold to show the new image
              triggerUnfold();
            };
            img.src = e.target.result;
          };
          reader.readAsDataURL(file);
        }
      });

      // Texture upload functionality - creates wrinkle overlay that blends with image
      textureUpload.addEventListener("change", function (e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function (e) {
            // Update the paper overlay texture
            const textureUrl = e.target.result;

            // Remove any existing custom texture styles
            const existingStyles = document.querySelectorAll(
              "style[data-custom-texture]"
            );
            existingStyles.forEach((style) => style.remove());

            // Update the CSS to use custom texture that blends with the image
            const style = document.createElement("style");
            style.setAttribute("data-custom-texture", "true");
            style.textContent = `
              .paper-overlay::before {
                background:
                  /* Custom user texture for wrinkles */
                  url(${textureUrl}),
                  /* Keep the original wrinkle patterns */
                  radial-gradient(ellipse at 20% 30%, rgba(0,0,0,0.15) 1px, transparent 4px),
                  radial-gradient(ellipse at 80% 70%, rgba(0,0,0,0.12) 1px, transparent 3px),
                  radial-gradient(ellipse at 60% 20%, rgba(0,0,0,0.10) 0.5px, transparent 2px),
                  radial-gradient(ellipse at 30% 80%, rgba(0,0,0,0.13) 1px, transparent 3px),
                  radial-gradient(ellipse at 70% 50%, rgba(0,0,0,0.08) 0.8px, transparent 2px),
                  linear-gradient(45deg, transparent 40%, rgba(0,0,0,0.08) 50%, transparent 60%),
                  linear-gradient(-30deg, transparent 35%, rgba(0,0,0,0.06) 45%, transparent 55%),
                  linear-gradient(120deg, transparent 30%, rgba(0,0,0,0.07) 40%, transparent 50%) !important;
                background-size:
                  40px 40px,
                  30px 25px, 35px 30px, 20px 18px, 40px 32px, 25px 22px,
                  60px 45px, 50px 40px, 45px 35px !important;
                opacity: 1 !important;
                mix-blend-mode: multiply !important;
              }
            `;
            document.head.appendChild(style);
          };
          reader.readAsDataURL(file);
        }
      });

      // Function to resize animation based on image dimensions
      function resizeAnimationToImage(imageWidth, imageHeight) {
        const maxWidth = Math.min(800, window.innerWidth * 0.6); // Max 800px or 60% of viewport
        const maxHeight = Math.min(600, window.innerHeight * 0.6); // Max 600px or 60% of viewport
        const minWidth = 400;
        const minHeight = 300;

        // Calculate aspect ratio
        const aspectRatio = imageWidth / imageHeight;

        let containerWidth, containerHeight;
        let revealWidth, revealHeight;

        // Determine container size based on aspect ratio
        if (aspectRatio > 1.5) {
          // Wide image
          containerWidth = Math.min(
            maxWidth,
            Math.max(minWidth, imageWidth * 0.8)
          );
          containerHeight = containerWidth / aspectRatio + 120; // Reduced padding for single overlay
          revealWidth = containerWidth * 0.75;
          revealHeight = revealWidth / aspectRatio;
        } else if (aspectRatio < 0.75) {
          // Tall image
          containerHeight = Math.min(
            maxHeight,
            Math.max(minHeight, imageHeight * 0.8)
          );
          containerWidth = containerHeight * aspectRatio + 160; // Reduced padding for single overlay
          revealHeight = containerHeight * 0.75;
          revealWidth = revealHeight * aspectRatio;
        } else {
          // Square-ish image
          const size = Math.min(
            maxWidth,
            maxHeight,
            Math.max(minWidth, Math.max(imageWidth, imageHeight) * 0.8)
          );
          containerWidth = size;
          containerHeight = size;
          revealWidth = size * 0.75;
          revealHeight = revealWidth / aspectRatio;
        }

        // Apply new dimensions
        paperContainer.style.width = containerWidth + "px";
        paperContainer.style.height = containerHeight + "px";

        // Update reveal area
        revealArea.style.width = revealWidth + "px";
        revealArea.style.height = revealHeight + "px";
        revealArea.style.top = (containerHeight - revealHeight) / 2 + "px";
        revealArea.style.left = (containerWidth - revealWidth) / 2 + "px";

        // Update paper overlay to extend beyond reveal area for sticker effect
        const overlayWidth = revealWidth + 40;
        const overlayHeight = revealHeight + 40;
        paperOverlay.style.width = overlayWidth + "px";
        paperOverlay.style.height = overlayHeight + "px";
        paperOverlay.style.top = (containerHeight - overlayHeight) / 2 + "px";
        paperOverlay.style.left = (containerWidth - overlayWidth) / 2 + "px";
      }

      // Animation control functions
      function triggerUnfold() {
        paperContainer.classList.remove("single-mode");
        paperContainer.classList.add("single-mode");

        // Remove the class after animation completes (5 seconds for reverse uncrumpling)
        setTimeout(() => {
          paperContainer.classList.remove("single-mode");
        }, 5000);
      }

      function pauseAnimation() {
        isPaused = true;
        paperContainer.classList.add("paused");
      }

      function resumeAnimation() {
        isPaused = false;
        paperContainer.classList.remove("paused");
      }

      function resetAnimation() {
        paperContainer.classList.remove("single-mode", "paused");
        isPaused = false;

        // Reset the overlay to initial state
        paperOverlay.style.animation = "none";
        paperOverlay.offsetHeight; // Force reflow
        paperOverlay.style.animation = null;
      }

      function changeSpeed() {
        speedIndex = (speedIndex + 1) % speeds.length;
        currentSpeed = speeds[speedIndex];

        // Update animation speed for reverse uncrumpling (8 seconds base)
        paperOverlay.style.animationDuration = 8 / currentSpeed + "s";

        // Update wrinkle animation speed
        const wrinkleStyle = document.createElement("style");
        wrinkleStyle.textContent = `
          .paper-overlay::before {
            animation-duration: ${8 / currentSpeed}s !important;
          }
        `;
        document.head.appendChild(wrinkleStyle);

        // Update button text
        document.querySelector(
          '[onclick="changeSpeed()"]'
        ).textContent = `⚡ Speed: ${currentSpeed}x`;
      }

      // Keyboard controls
      document.addEventListener("keydown", (e) => {
        switch (e.key) {
          case " ":
            e.preventDefault();
            if (isPaused) {
              resumeAnimation();
            } else {
              pauseAnimation();
            }
            break;
          case "r":
          case "R":
            resetAnimation();
            break;
          case "Enter":
            triggerUnfold();
            break;
        }
      });

      // Drag and drop functionality
      document.addEventListener("dragover", (e) => {
        e.preventDefault();
        document.body.style.backgroundColor = "#2a2a2a";
      });

      document.addEventListener("dragleave", (e) => {
        if (!e.relatedTarget) {
          document.body.style.background =
            "linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)";
        }
      });

      document.addEventListener("drop", (e) => {
        e.preventDefault();
        document.body.style.background =
          "linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)";

        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith("image/")) {
          const reader = new FileReader();
          reader.onload = function (e) {
            const img = new Image();
            img.onload = function () {
              resizeAnimationToImage(img.width, img.height);
              revealArea.style.backgroundImage = `url(${e.target.result})`;
              revealArea.style.backgroundSize = "contain";
              revealArea.style.backgroundPosition = "center";
              revealArea.style.backgroundRepeat = "no-repeat";
              triggerUnfold();
            };
            img.src = e.target.result;
          };
          reader.readAsDataURL(files[0]);
        }
      });

      // Add some interactive feedback
      document.querySelectorAll(".btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          btn.style.transform = "translateY(0) scale(0.95)";
          setTimeout(() => {
            btn.style.transform = "";
          }, 150);
        });
      });

      // Auto-trigger unfold on page load
      setTimeout(() => {
        triggerUnfold();
      }, 1000);
    </script>
  </body>
</html>
