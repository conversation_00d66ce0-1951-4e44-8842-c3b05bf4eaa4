<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Organic Paper Fold Animation</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(
          135deg,
          #0f0f23 0%,
          #1a1a2e 50%,
          #16213e 100%
        );
        min-height: 100vh;
        margin: 0;
        padding: 40px 20px;
        color: white;
        overflow-x: hidden;
      }

      .main-container {
        max-width: 1400px;
        margin: 0 auto;
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        grid-template-rows: auto 1fr auto;
        gap: 40px;
        min-height: calc(100vh - 80px);
        grid-template-areas:
          "header header header"
          "sidebar animation controls"
          "footer footer footer";
      }

      .header {
        grid-area: header;
        text-align: center;
        padding: 20px 0;
      }

      .animation-area {
        grid-area: animation;
        display: flex;
        align-items: center;
        justify-content: center;
        perspective: 1500px;
        perspective-origin: center center;
        min-height: 500px;
      }

      .paper-container {
        position: relative;
        width: 600px;
        height: 420px;
        transform-style: preserve-3d;
      }

      .paper-piece {
        position: absolute;
        background: #f8f8f8;
        border: 1px solid #ddd;
        transform-style: preserve-3d;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.2));
      }

      /* Header styling */
      .header h1 {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 10px 0;
        letter-spacing: -0.02em;
      }

      .header p {
        font-size: 1.2rem;
        opacity: 0.8;
        margin: 0;
        font-weight: 300;
      }

      /* Main image reveal area */
      .reveal-area {
        width: 360px;
        height: 240px;
        top: 90px;
        left: 120px;
        background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzYwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDM2MCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzNjAiIGhlaWdodD0iMjQwIiBmaWxsPSIjNGY0NmU1Ii8+Cjx0ZXh0IHg9IjE4MCIgeT0iMTIwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPllPVVIgSU1BR0U8L3RleHQ+CjwvU3ZnPgo=");
        background-size: cover;
        background-position: center;
        z-index: 1;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }

      /* Paper pieces that unfold with highly organic movement */
      .piece-1 {
        width: 180px;
        height: 120px;
        top: 90px;
        left: 120px;
        transform-origin: 47% 94%; /* More irregular origin point */
        background: linear-gradient(
          52deg,
          #fff 0%,
          #f4f4f4 35%,
          #f0f0f0 65%,
          #e8e8e8 100%
        );
        animation: organicUnfold1 3.7s cubic-bezier(0.19, 0.61, 0.38, 0.97)
          infinite;
        z-index: 3;
        clip-path: polygon(
          1% 2%,
          97% 0%,
          99% 3%,
          96% 97%,
          3% 99%,
          0% 95%
        ); /* More irregular edges */
        filter: drop-shadow(0 3px 8px rgba(0, 0, 0, 0.15));
        border-radius: 4px;
      }

      .piece-2 {
        width: 180px;
        height: 120px;
        top: 90px;
        right: 120px;
        transform-origin: 53% 96%;
        background: linear-gradient(
          -38deg,
          #fff 0%,
          #f3f3f3 30%,
          #f1f1f1 70%,
          #e9e9e9 100%
        );
        animation: organicUnfold2 3.4s cubic-bezier(0.22, 0.58, 0.41, 0.95)
          infinite;
        animation-delay: 0.18s;
        z-index: 3;
        clip-path: polygon(3% 0%, 100% 1%, 98% 4%, 99% 96%, 2% 98%, 0% 94%);
        filter: drop-shadow(0 4px 9px rgba(0, 0, 0, 0.12));
        border-radius: 4px;
      }

      .piece-3 {
        width: 180px;
        height: 120px;
        bottom: 90px;
        left: 120px;
        transform-origin: 46% 6%;
        background: linear-gradient(
          128deg,
          #fff 0%,
          #f2f2f2 40%,
          #f0f0f0 60%,
          #e7e7e7 100%
        );
        animation: organicUnfold3 3.6s cubic-bezier(0.21, 0.55, 0.44, 0.98)
          infinite;
        animation-delay: 0.42s;
        z-index: 3;
        clip-path: polygon(2% 5%, 98% 1%, 100% 97%, 97% 100%, 1% 98%, 0% 6%);
        filter: drop-shadow(0 2px 7px rgba(0, 0, 0, 0.18));
        border-radius: 4px;
      }

      .piece-4 {
        width: 180px;
        height: 120px;
        bottom: 90px;
        right: 120px;
        transform-origin: 54% 4%;
        background: linear-gradient(
          -142deg,
          #fff 0%,
          #f5f5f5 25%,
          #f3f3f3 75%,
          #e6e6e6 100%
        );
        animation: organicUnfold4 3.1s cubic-bezier(0.18, 0.64, 0.47, 0.93)
          infinite;
        animation-delay: 0.67s;
        z-index: 3;
        clip-path: polygon(0% 3%, 97% 0%, 100% 2%, 98% 98%, 2% 100%, 1% 96%);
        filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.14));
        border-radius: 4px;
      }

      /* Highly organic crumpled paper texture with natural imperfections */
      .paper-piece::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          /* Organic wrinkle patterns */ radial-gradient(
            ellipse at 19% 73%,
            rgba(0, 0, 0, 0.09) 0.8px,
            transparent 2.3px
          ),
          radial-gradient(
            ellipse at 81% 27%,
            rgba(0, 0, 0, 0.07) 1.2px,
            transparent 2.8px
          ),
          radial-gradient(
            ellipse at 42% 31%,
            rgba(0, 0, 0, 0.05) 0.6px,
            transparent 1.4px
          ),
          radial-gradient(
            ellipse at 68% 89%,
            rgba(0, 0, 0, 0.08) 1.1px,
            transparent 2.5px
          ),
          radial-gradient(
            ellipse at 12% 41%,
            rgba(0, 0, 0, 0.06) 0.9px,
            transparent 1.8px
          ),
          radial-gradient(
            ellipse at 91% 58%,
            rgba(0, 0, 0, 0.04) 0.7px,
            transparent 1.6px
          ),
          /* Irregular fold lines */
            linear-gradient(
              47deg,
              transparent 32%,
              rgba(0, 0, 0, 0.04) 46%,
              transparent 62%
            ),
          linear-gradient(
            -71deg,
            transparent 38%,
            rgba(0, 0, 0, 0.05) 51%,
            transparent 69%
          ),
          linear-gradient(
            123deg,
            transparent 29%,
            rgba(0, 0, 0, 0.03) 44%,
            transparent 58%
          ),
          /* Micro-texture for paper fiber */
            radial-gradient(
              circle at 33% 67%,
              rgba(0, 0, 0, 0.02) 0.3px,
              transparent 0.8px
            ),
          radial-gradient(
            circle at 78% 34%,
            rgba(0, 0, 0, 0.02) 0.4px,
            transparent 0.9px
          );
        background-size: 23px 28px, 31px 24px, 16px 19px, 37px 29px, 25px 21px,
          19px 23px, 41px 33px, 35px 39px, 28px 31px, 8px 11px, 12px 9px;
        pointer-events: none;
        opacity: 0.8;
        animation: subtleTextureShift 8s ease-in-out infinite;
      }

      /* More realistic torn/worn paper edges */
      .paper-piece::after {
        content: "";
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(
          45deg,
          #fafafa 0%,
          #f5f5f5 50%,
          #f0f0f0 100%
        );
        z-index: -1;
        filter: blur(0.5px);
        opacity: 0.6;
        transform: scale(1.02);
      }

      /* Add subtle texture animation for organic feel */
      @keyframes subtleTextureShift {
        0%,
        100% {
          background-position: 0px 0px, 5px 3px, 2px 7px, 8px 1px, 3px 9px,
            6px 4px, 12px 8px, 15px 11px, 9px 14px, 4px 2px, 7px 6px;
        }
        25% {
          background-position: 1px 2px, 6px 1px, 3px 5px, 7px 3px, 4px 8px,
            5px 6px, 13px 9px, 14px 12px, 8px 15px, 3px 4px, 8px 5px;
        }
        50% {
          background-position: 2px 1px, 4px 4px, 1px 8px, 9px 2px, 5px 7px,
            7px 5px, 11px 10px, 16px 10px, 10px 13px, 5px 3px, 6px 7px;
        }
        75% {
          background-position: 1px 3px, 7px 2px, 4px 6px, 6px 4px, 2px 9px,
            8px 3px, 14px 7px, 13px 13px, 7px 16px, 2px 5px, 9px 4px;
        }
      }

      /* Highly organic, human-like animations with natural imperfections */
      @keyframes organicUnfold1 {
        0%,
        26% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)
            scale(1);
        }
        29% {
          transform: rotateX(-3deg) rotateY(-1deg) rotateZ(0.5deg)
            translateZ(1px) scale(1.002);
        }
        33% {
          transform: rotateX(-7deg) rotateY(-3deg) rotateZ(1.2deg)
            translateZ(3px) scale(1.005);
        }
        41% {
          transform: rotateX(-45deg) rotateY(-12deg) rotateZ(-1deg)
            translateZ(5px) scale(1.008);
        }
        47% {
          transform: rotateX(-98deg) rotateY(-19deg) rotateZ(-2.3deg)
            translateZ(9px) scale(1.012);
        }
        53% {
          transform: rotateX(-172deg) rotateY(-23deg) rotateZ(0.8deg)
            translateZ(13px) scale(1.015);
        }
        59% {
          transform: rotateX(-185deg) rotateY(-20deg) rotateZ(-1.5deg)
            translateZ(11px) scale(1.012);
        }
        65% {
          transform: rotateX(-179deg) rotateY(-22deg) rotateZ(0.3deg)
            translateZ(9px) scale(1.008);
        }
        85% {
          transform: rotateX(-181deg) rotateY(-21deg) rotateZ(-0.2deg)
            translateZ(8px) scale(1.005);
        }
        91% {
          transform: rotateX(-12deg) rotateY(-2deg) rotateZ(0.8deg)
            translateZ(2px) scale(1.002);
        }
        96% {
          transform: rotateX(-3deg) rotateY(-0.5deg) rotateZ(0.2deg)
            translateZ(1px) scale(1.001);
        }
        100% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)
            scale(1);
        }
      }

      @keyframes organicUnfold2 {
        0%,
        28% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)
            scale(1);
        }
        31% {
          transform: rotateX(-2deg) rotateY(0.8deg) rotateZ(-0.7deg)
            translateZ(1px) scale(1.001);
        }
        36% {
          transform: rotateX(-5deg) rotateY(2deg) rotateZ(-1.3deg)
            translateZ(2px) scale(1.004);
        }
        43% {
          transform: rotateX(-42deg) rotateY(11deg) rotateZ(1.1deg)
            translateZ(4px) scale(1.007);
        }
        49% {
          transform: rotateX(-91deg) rotateY(20deg) rotateZ(2.2deg)
            translateZ(8px) scale(1.011);
        }
        55% {
          transform: rotateX(-186deg) rotateY(24deg) rotateZ(-0.9deg)
            translateZ(12px) scale(1.014);
        }
        61% {
          transform: rotateX(-180deg) rotateY(21deg) rotateZ(1.4deg)
            translateZ(10px) scale(1.011);
        }
        67% {
          transform: rotateX(-178deg) rotateY(23deg) rotateZ(-0.3deg)
            translateZ(8px) scale(1.007);
        }
        83% {
          transform: rotateX(-182deg) rotateY(22deg) rotateZ(0.1deg)
            translateZ(7px) scale(1.004);
        }
        92% {
          transform: rotateX(-9deg) rotateY(1.5deg) rotateZ(-0.8deg)
            translateZ(2px) scale(1.001);
        }
        97% {
          transform: rotateX(-2deg) rotateY(0.3deg) rotateZ(-0.2deg)
            translateZ(1px) scale(1.0005);
        }
        100% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)
            scale(1);
        }
      }

      @keyframes organicUnfold3 {
        0%,
        30% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)
            scale(1);
        }
        34% {
          transform: rotateX(2.5deg) rotateY(-0.6deg) rotateZ(0.9deg)
            translateZ(1px) scale(1.002);
        }
        38% {
          transform: rotateX(6deg) rotateY(-1.8deg) rotateZ(1.5deg)
            translateZ(3px) scale(1.006);
        }
        45% {
          transform: rotateX(48deg) rotateY(-9deg) rotateZ(-1.2deg)
            translateZ(6px) scale(1.009);
        }
        51% {
          transform: rotateX(95deg) rotateY(-18deg) rotateZ(-2.4deg)
            translateZ(10px) scale(1.013);
        }
        57% {
          transform: rotateX(182deg) rotateY(-25deg) rotateZ(0.7deg)
            translateZ(14px) scale(1.016);
        }
        63% {
          transform: rotateX(188deg) rotateY(-21deg) rotateZ(-1.3deg)
            translateZ(12px) scale(1.013);
        }
        69% {
          transform: rotateX(184deg) rotateY(-23deg) rotateZ(0.4deg)
            translateZ(9px) scale(1.009);
        }
        81% {
          transform: rotateX(183deg) rotateY(-22deg) rotateZ(-0.1deg)
            translateZ(8px) scale(1.006);
        }
        94% {
          transform: rotateX(11deg) rotateY(-1.7deg) rotateZ(0.9deg)
            translateZ(2px) scale(1.002);
        }
        98% {
          transform: rotateX(3deg) rotateY(-0.4deg) rotateZ(0.3deg)
            translateZ(1px) scale(1.001);
        }
        100% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)
            scale(1);
        }
      }

      @keyframes organicUnfold4 {
        0%,
        32% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)
            scale(1);
        }
        36% {
          transform: rotateX(3deg) rotateY(1.2deg) rotateZ(-0.8deg)
            translateZ(1px) scale(1.003);
        }
        40% {
          transform: rotateX(7deg) rotateY(2.5deg) rotateZ(-1.6deg)
            translateZ(2px) scale(1.007);
        }
        47% {
          transform: rotateX(52deg) rotateY(13deg) rotateZ(1.3deg)
            translateZ(5px) scale(1.01);
        }
        53% {
          transform: rotateX(99deg) rotateY(22deg) rotateZ(2.1deg)
            translateZ(9px) scale(1.014);
        }
        59% {
          transform: rotateX(179deg) rotateY(26deg) rotateZ(-0.7deg)
            translateZ(13px) scale(1.017);
        }
        65% {
          transform: rotateX(187deg) rotateY(23deg) rotateZ(1.2deg)
            translateZ(11px) scale(1.014);
        }
        71% {
          transform: rotateX(182deg) rotateY(25deg) rotateZ(-0.2deg)
            translateZ(8px) scale(1.01);
        }
        79% {
          transform: rotateX(181deg) rotateY(24deg) rotateZ(0.3deg)
            translateZ(7px) scale(1.007);
        }
        96% {
          transform: rotateX(8deg) rotateY(2.8deg) rotateZ(-0.9deg)
            translateZ(2px) scale(1.003);
        }
        99% {
          transform: rotateX(2deg) rotateY(0.7deg) rotateZ(-0.3deg)
            translateZ(1px) scale(1.001);
        }
        100% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)
            scale(1);
        }
      }

      /* Sidebar with upload area */
      .sidebar {
        grid-area: sidebar;
        display: flex;
        flex-direction: column;
        gap: 30px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        height: fit-content;
      }

      .upload-section h3 {
        font-size: 1.4rem;
        margin-bottom: 15px;
        color: #667eea;
        font-weight: 600;
      }

      .file-input {
        display: none;
      }

      .upload-btn {
        display: block;
        width: 100%;
        padding: 16px 24px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 12px;
        cursor: pointer;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        margin-bottom: 15px;
        text-align: center;
      }

      .upload-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
      }

      .upload-description {
        font-size: 0.95rem;
        opacity: 0.7;
        line-height: 1.5;
        text-align: center;
      }

      /* Controls panel */
      .controls-panel {
        grid-area: controls;
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        height: fit-content;
      }

      .controls-panel h3 {
        font-size: 1.4rem;
        margin-bottom: 10px;
        color: #667eea;
        font-weight: 600;
      }

      .controls {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .btn {
        padding: 14px 20px;
        background: rgba(255, 255, 255, 0.08);
        border: 2px solid rgba(255, 255, 255, 0.15);
        color: white;
        border-radius: 10px;
        cursor: pointer;
        font-weight: 500;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        text-align: center;
      }

      .btn:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      .btn:active {
        transform: translateY(0);
      }

      /* Single animation mode with organic movement */
      .paper-container.single-mode .piece-1 {
        animation: singleOrganicUnfold1 2.2s
          cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
      }
      .paper-container.single-mode .piece-2 {
        animation: singleOrganicUnfold2 2.1s
          cubic-bezier(0.23, 0.48, 0.47, 0.92) forwards;
        animation-delay: 0.15s;
      }
      .paper-container.single-mode .piece-3 {
        animation: singleOrganicUnfold3 2.3s
          cubic-bezier(0.26, 0.44, 0.43, 0.96) forwards;
        animation-delay: 0.35s;
      }
      .paper-container.single-mode .piece-4 {
        animation: singleOrganicUnfold4 1.9s
          cubic-bezier(0.24, 0.49, 0.46, 0.91) forwards;
        animation-delay: 0.55s;
      }

      @keyframes singleOrganicUnfold1 {
        0% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px);
        }
        15% {
          transform: rotateX(-8deg) rotateY(-3deg) rotateZ(1deg) translateZ(3px);
        }
        45% {
          transform: rotateX(-95deg) rotateY(-18deg) rotateZ(-2deg)
            translateZ(8px);
        }
        65% {
          transform: rotateX(-175deg) rotateY(-22deg) rotateZ(1deg)
            translateZ(12px);
        }
        80% {
          transform: rotateX(-182deg) rotateY(-19deg) rotateZ(-1deg)
            translateZ(10px);
        }
        100% {
          transform: rotateX(-178deg) rotateY(-21deg) rotateZ(0deg)
            translateZ(8px);
        }
      }

      @keyframes singleOrganicUnfold2 {
        0% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px);
        }
        18% {
          transform: rotateX(-5deg) rotateY(2deg) rotateZ(-1deg) translateZ(2px);
        }
        48% {
          transform: rotateX(-88deg) rotateY(19deg) rotateZ(2deg)
            translateZ(7px);
        }
        68% {
          transform: rotateX(-183deg) rotateY(23deg) rotateZ(-1deg)
            translateZ(11px);
        }
        85% {
          transform: rotateX(-177deg) rotateY(20deg) rotateZ(1deg)
            translateZ(9px);
        }
        100% {
          transform: rotateX(-181deg) rotateY(22deg) rotateZ(0deg)
            translateZ(7px);
        }
      }

      @keyframes singleOrganicUnfold3 {
        0% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px);
        }
        12% {
          transform: rotateX(6deg) rotateY(-2deg) rotateZ(1deg) translateZ(3px);
        }
        42% {
          transform: rotateX(92deg) rotateY(-17deg) rotateZ(-2deg)
            translateZ(9px);
        }
        62% {
          transform: rotateX(179deg) rotateY(-24deg) rotateZ(1deg)
            translateZ(13px);
        }
        78% {
          transform: rotateX(185deg) rotateY(-20deg) rotateZ(-1deg)
            translateZ(11px);
        }
        100% {
          transform: rotateX(182deg) rotateY(-22deg) rotateZ(0deg)
            translateZ(8px);
        }
      }

      @keyframes singleOrganicUnfold4 {
        0% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px);
        }
        20% {
          transform: rotateX(9deg) rotateY(3deg) rotateZ(-1deg) translateZ(2px);
        }
        50% {
          transform: rotateX(96deg) rotateY(21deg) rotateZ(2deg) translateZ(8px);
        }
        70% {
          transform: rotateX(176deg) rotateY(25deg) rotateZ(-1deg)
            translateZ(12px);
        }
        88% {
          transform: rotateX(184deg) rotateY(22deg) rotateZ(1deg)
            translateZ(10px);
        }
        100% {
          transform: rotateX(180deg) rotateY(24deg) rotateZ(0deg)
            translateZ(7px);
        }
      }

      /* Pause mode */
      .paper-container.paused .paper-piece {
        animation-play-state: paused;
      }

      /* Instructions within sidebar */
      .instructions {
        margin-top: 20px;
      }

      .instructions h4 {
        color: #667eea;
        margin-bottom: 15px;
        font-size: 1.2rem;
        font-weight: 600;
      }

      .instructions ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .instructions li {
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
        font-size: 0.9rem;
        opacity: 0.8;
        line-height: 1.4;
      }

      .instructions li::before {
        content: "→";
        position: absolute;
        left: 0;
        color: #667eea;
        font-weight: bold;
      }

      /* Footer */
      .footer {
        grid-area: footer;
        text-align: center;
        padding: 20px 0;
        opacity: 0.6;
        font-size: 0.9rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: 20px;
      }

      @media (max-width: 1200px) {
        .main-container {
          grid-template-columns: 1fr;
          grid-template-areas:
            "header"
            "animation"
            "sidebar"
            "controls"
            "footer";
          gap: 30px;
        }

        .sidebar,
        .controls-panel {
          max-width: 600px;
          margin: 0 auto;
        }

        .header h1 {
          font-size: 2.5rem;
        }
      }

      @media (max-width: 768px) {
        body {
          padding: 20px 15px;
        }

        .main-container {
          gap: 20px;
        }

        .paper-container {
          width: 480px;
          height: 336px;
        }

        .reveal-area {
          width: 288px;
          height: 192px;
          top: 72px;
          left: 96px;
        }

        .piece-1,
        .piece-2,
        .piece-3,
        .piece-4 {
          width: 144px;
          height: 96px;
        }

        .piece-1 {
          top: 72px;
          left: 96px;
        }
        .piece-2 {
          top: 72px;
          right: 96px;
        }
        .piece-3 {
          bottom: 72px;
          left: 96px;
        }
        .piece-4 {
          bottom: 72px;
          right: 96px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .header p {
          font-size: 1rem;
        }

        .sidebar,
        .controls-panel {
          padding: 20px;
        }
      }

      @media (max-width: 480px) {
        .paper-container {
          width: 360px;
          height: 252px;
        }

        .reveal-area {
          width: 216px;
          height: 144px;
          top: 54px;
          left: 72px;
        }

        .piece-1,
        .piece-2,
        .piece-3,
        .piece-4 {
          width: 108px;
          height: 72px;
        }

        .piece-1 {
          top: 54px;
          left: 72px;
        }
        .piece-2 {
          top: 54px;
          right: 72px;
        }
        .piece-3 {
          bottom: 54px;
          left: 72px;
        }
        .piece-4 {
          bottom: 54px;
          right: 72px;
        }

        .header h1 {
          font-size: 1.8rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="main-container">
      <!-- Header -->
      <div class="header">
        <h1>Organic Paper Fold Animation</h1>
        <p>Watch your images come to life through natural paper unfolding</p>
      </div>

      <!-- Sidebar with Upload -->
      <div class="sidebar">
        <div class="upload-section">
          <h3>📸 Upload Image</h3>
          <label for="imageUpload" class="upload-btn">
            Choose Your Image
          </label>
          <input
            type="file"
            id="imageUpload"
            class="file-input"
            accept="image/*"
          />
          <div class="upload-description">
            Upload an image to see it revealed through organic paper animation.
            Supports JPG, PNG, and other common formats.
          </div>
        </div>

        <div class="instructions">
          <h4>How to Use:</h4>
          <ul>
            <li>Upload your image above</li>
            <li>Watch organic paper unfold</li>
            <li>Use controls on the right</li>
            <li>Press SPACE to pause</li>
            <li>Press R to reset</li>
            <li>Press ENTER to trigger once</li>
          </ul>
        </div>
      </div>

      <!-- Animation Area -->
      <div class="animation-area">
        <div class="paper-container" id="paperContainer">
          <div class="paper-piece reveal-area" id="revealArea"></div>
          <div class="paper-piece piece-1"></div>
          <div class="paper-piece piece-2"></div>
          <div class="paper-piece piece-3"></div>
          <div class="paper-piece piece-4"></div>
        </div>
      </div>

      <!-- Controls Panel -->
      <div class="controls-panel">
        <h3>🎮 Animation Controls</h3>
        <div class="controls">
          <button class="btn" onclick="triggerUnfold()">🎬 Unfold Once</button>
          <button class="btn" onclick="pauseAnimation()">⏸️ Pause</button>
          <button class="btn" onclick="resumeAnimation()">▶️ Resume</button>
          <button class="btn" onclick="resetAnimation()">🔄 Reset</button>
          <button class="btn" onclick="changeSpeed()">⚡ Speed: 1x</button>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <p>
          Drag and drop images anywhere on the page • Keyboard shortcuts
          available
        </p>
      </div>
    </div>

    <script>
      const paperContainer = document.getElementById("paperContainer");
      const revealArea = document.getElementById("revealArea");
      const imageUpload = document.getElementById("imageUpload");
      let isPaused = false;
      let currentSpeed = 1;
      const speeds = [0.5, 1, 1.5, 2];
      let speedIndex = 1;

      // Image upload functionality
      imageUpload.addEventListener("change", function (e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function (e) {
            revealArea.style.backgroundImage = `url(${e.target.result})`;
            revealArea.style.backgroundSize = "cover";
            revealArea.style.backgroundPosition = "center";
            // Trigger a single unfold to show the new image
            triggerUnfold();
          };
          reader.readAsDataURL(file);
        }
      });

      // Animation controls
      function triggerUnfold() {
        paperContainer.classList.remove("paused");
        paperContainer.classList.add("single-mode");

        setTimeout(() => {
          paperContainer.classList.remove("single-mode");
        }, 4000); // Slightly longer for organic movement
      }

      function pauseAnimation() {
        paperContainer.classList.add("paused");
        isPaused = true;
      }

      function resumeAnimation() {
        paperContainer.classList.remove("paused");
        isPaused = false;
      }

      function resetAnimation() {
        paperContainer.classList.remove("paused", "single-mode");
        isPaused = false;

        // Force reflow to restart animations
        paperContainer.style.animation = "none";
        paperContainer.offsetHeight;
        paperContainer.style.animation = null;
      }

      function changeSpeed() {
        speedIndex = (speedIndex + 1) % speeds.length;
        currentSpeed = speeds[speedIndex];

        const pieces = document.querySelectorAll(".paper-piece");
        pieces.forEach((piece) => {
          piece.style.animationDuration = `${3 / currentSpeed}s`;
        });

        // Update button text to show current speed
        event.target.textContent = `⚡ ${currentSpeed}x`;
      }

      // Keyboard controls
      document.addEventListener("keydown", (e) => {
        switch (e.key) {
          case " ":
            e.preventDefault();
            if (isPaused) {
              resumeAnimation();
            } else {
              pauseAnimation();
            }
            break;
          case "r":
          case "R":
            resetAnimation();
            break;
          case "Enter":
            triggerUnfold();
            break;
        }
      });

      // Drag and drop functionality
      document.addEventListener("dragover", (e) => {
        e.preventDefault();
        document.body.style.backgroundColor = "#2a2a2a";
      });

      document.addEventListener("dragleave", (e) => {
        if (!e.relatedTarget) {
          document.body.style.backgroundColor = "#1a1a1a";
        }
      });

      document.addEventListener("drop", (e) => {
        e.preventDefault();
        document.body.style.backgroundColor = "#1a1a1a";

        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith("image/")) {
          const reader = new FileReader();
          reader.onload = function (e) {
            revealArea.style.backgroundImage = `url(${e.target.result})`;
            revealArea.style.backgroundSize = "cover";
            revealArea.style.backgroundPosition = "center";
            triggerUnfold();
          };
          reader.readAsDataURL(files[0]);
        }
      });

      // Add some interactive feedback
      document.querySelectorAll(".btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          btn.style.transform = "translateY(0) scale(0.95)";
          setTimeout(() => {
            btn.style.transform = "";
          }, 150);
        });
      });

      // Auto-trigger unfold on page load
      setTimeout(() => {
        triggerUnfold();
      }, 1000);
    </script>
  </body>
</html>
