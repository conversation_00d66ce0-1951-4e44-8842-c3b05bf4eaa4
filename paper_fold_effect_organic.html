<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Organic Paper Fold Animation</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(
          135deg,
          #0f0f23 0%,
          #1a1a2e 50%,
          #16213e 100%
        );
        min-height: 100vh;
        margin: 0;
        padding: 40px 20px;
        color: white;
        overflow-x: hidden;
      }

      .main-container {
        max-width: 1400px;
        margin: 0 auto;
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        grid-template-rows: auto 1fr auto;
        gap: 40px;
        min-height: calc(100vh - 80px);
        grid-template-areas:
          "header header header"
          "sidebar animation controls"
          "footer footer footer";
      }

      .header {
        grid-area: header;
        text-align: center;
        padding: 20px 0;
      }

      .animation-area {
        grid-area: animation;
        display: flex;
        align-items: center;
        justify-content: center;
        perspective: 1500px;
        perspective-origin: center center;
        min-height: 500px;
      }

      .paper-container {
        position: relative;
        width: 600px;
        height: 420px;
        transform-style: preserve-3d;
        transition: all 0.5s ease;
        max-width: 90vw;
        max-height: 70vh;
      }

      .paper-piece {
        position: absolute;
        background: #f8f8f8;
        border: 1px solid #ddd;
        transform-style: preserve-3d;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.2));
      }

      /* Header styling */
      .header h1 {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 10px 0;
        letter-spacing: -0.02em;
      }

      .header p {
        font-size: 1.2rem;
        opacity: 0.8;
        margin: 0;
        font-weight: 300;
      }

      /* Main image reveal area */
      .reveal-area {
        width: 360px;
        height: 240px;
        top: 90px;
        left: 120px;
        background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzYwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDM2MCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzNjAiIGhlaWdodD0iMjQwIiBmaWxsPSIjNGY0NmU1Ii8+Cjx0ZXh0IHg9IjE4MCIgeT0iMTIwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPllPVVIgSU1BR0U8L3RleHQ+CjwvU3ZnPgo=");
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 1;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transition: all 0.5s ease;
      }

      /* Single organic paper that unfolds from center */
      .paper-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          #fff 0%,
          #f8f8f8 25%,
          #f2f2f2 50%,
          #eeeeee 75%,
          #e8e8e8 100%
        );
        z-index: 3;
        transform-origin: center center;
        animation: organicCenterUnfold 4s cubic-bezier(0.25, 0.46, 0.45, 0.94)
          infinite;
        clip-path: circle(0% at center);
        filter: drop-shadow(0 8px 25px rgba(0, 0, 0, 0.3));
        border-radius: 8px;
        transition: all 0.5s ease;
      }

      /* Paper texture overlay */
      .paper-overlay::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxkZWZzPgo8cGF0dGVybiBpZD0icGFwZXIiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCI+CjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjAyIi8+CjxjaXJjbGUgY3g9IjMiIGN5PSI3IiByPSIwLjUiIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4wNSIvPgo8Y2lyY2xlIGN4PSIxNSIgY3k9IjEzIiByPSIwLjMiIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4wNCIvPgo8L3BhdHRlcm4+CjwvZGVmcz4KPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXBlcikiLz4KPC9zdmc+");
        background-size: 20px 20px;
        opacity: 0.6;
        pointer-events: none;
        border-radius: 8px;
        animation: subtleTextureShift 8s ease-in-out infinite;
      }

      /* Organic wrinkles and folds that appear during animation */
      .paper-overlay::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            ellipse at 30% 20%,
            rgba(0, 0, 0, 0.08) 1px,
            transparent 3px
          ),
          radial-gradient(
            ellipse at 70% 80%,
            rgba(0, 0, 0, 0.06) 1px,
            transparent 2px
          ),
          radial-gradient(
            ellipse at 20% 70%,
            rgba(0, 0, 0, 0.05) 0.5px,
            transparent 2px
          ),
          linear-gradient(
            45deg,
            transparent 40%,
            rgba(0, 0, 0, 0.03) 50%,
            transparent 60%
          ),
          linear-gradient(
            -45deg,
            transparent 35%,
            rgba(0, 0, 0, 0.04) 45%,
            transparent 55%
          );
        background-size: 40px 30px, 35px 40px, 25px 20px, 60px 50px, 55px 45px;
        opacity: 0;
        animation: wrinkleAppear 4s ease-in-out infinite;
        pointer-events: none;
        border-radius: 8px;
      }

      /* Organic center-out unfolding animation */
      @keyframes organicCenterUnfold {
        0%,
        15% {
          clip-path: circle(0% at center);
          transform: scale(1) rotate(0deg);
        }
        20% {
          clip-path: circle(5% at center);
          transform: scale(1.02) rotate(1deg);
        }
        30% {
          clip-path: circle(15% at center);
          transform: scale(1.05) rotate(-0.5deg);
        }
        45% {
          clip-path: circle(35% at center);
          transform: scale(1.08) rotate(0.8deg);
        }
        60% {
          clip-path: circle(60% at center);
          transform: scale(1.12) rotate(-0.3deg);
        }
        75% {
          clip-path: circle(85% at center);
          transform: scale(1.15) rotate(0.2deg);
        }
        85% {
          clip-path: circle(100% at center);
          transform: scale(1.18) rotate(-0.1deg);
        }
        92% {
          clip-path: circle(100% at center);
          transform: scale(1.05) rotate(0.1deg);
        }
        100% {
          clip-path: circle(0% at center);
          transform: scale(1) rotate(0deg);
        }
      }

      /* Wrinkle animation that appears during unfolding */
      @keyframes wrinkleAppear {
        0%,
        20% {
          opacity: 0;
        }
        30% {
          opacity: 0.3;
        }
        60% {
          opacity: 0.7;
        }
        85% {
          opacity: 0.4;
        }
        100% {
          opacity: 0;
        }
      }

      /* Single animation mode */
      .paper-container.single-mode .paper-overlay {
        animation: singleOrganicCenterUnfold 3s
          cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
      }

      @keyframes singleOrganicCenterUnfold {
        0% {
          clip-path: circle(0% at center);
          transform: scale(1) rotate(0deg);
        }
        15% {
          clip-path: circle(8% at center);
          transform: scale(1.03) rotate(1.2deg);
        }
        35% {
          clip-path: circle(25% at center);
          transform: scale(1.08) rotate(-0.7deg);
        }
        55% {
          clip-path: circle(50% at center);
          transform: scale(1.15) rotate(0.5deg);
        }
        75% {
          clip-path: circle(80% at center);
          transform: scale(1.2) rotate(-0.2deg);
        }
        90% {
          clip-path: circle(100% at center);
          transform: scale(1.25) rotate(0.1deg);
        }
        100% {
          clip-path: circle(100% at center);
          transform: scale(1.25) rotate(0deg);
        }
      }

      /* Texture upload section */
      .texture-upload {
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      .texture-upload h4 {
        color: #667eea;
        margin-bottom: 15px;
        font-size: 1.2rem;
        font-weight: 600;
      }

      .texture-btn {
        display: block;
        width: 100%;
        padding: 12px 20px;
        background: rgba(255, 255, 255, 0.08);
        border: 2px dashed rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-align: center;
        margin-bottom: 10px;
      }

      .texture-btn:hover {
        background: rgba(255, 255, 255, 0.12);
        border-color: rgba(255, 255, 255, 0.5);
      }

      .texture-description {
        font-size: 0.85rem;
        opacity: 0.6;
        line-height: 1.4;
        text-align: center;
      }

      /* Subtle texture animation for organic feel */
      @keyframes subtleTextureShift {
        0%,
        100% {
          background-position: 0px 0px;
        }
        25% {
          background-position: 2px 1px;
        }
        50% {
          background-position: 1px 3px;
        }
        75% {
          background-position: 3px 2px;
        }
      }

      /* Pause mode */
      .paper-container.paused .paper-overlay {
        animation-play-state: paused;
      }

      /* Sidebar with upload area */
      .sidebar {
        grid-area: sidebar;
        display: flex;
        flex-direction: column;
        gap: 30px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        height: fit-content;
      }

      .upload-section h3 {
        font-size: 1.4rem;
        margin-bottom: 15px;
        color: #667eea;
        font-weight: 600;
      }

      .file-input {
        display: none;
      }

      .upload-btn {
        display: block;
        width: 100%;
        padding: 16px 24px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 12px;
        cursor: pointer;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        margin-bottom: 15px;
        text-align: center;
      }

      .upload-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
      }

      .upload-description {
        font-size: 0.95rem;
        opacity: 0.7;
        line-height: 1.5;
        text-align: center;
      }

      /* Controls panel */
      .controls-panel {
        grid-area: controls;
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        height: fit-content;
      }

      .controls-panel h3 {
        font-size: 1.4rem;
        margin-bottom: 10px;
        color: #667eea;
        font-weight: 600;
      }

      .controls {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .btn {
        padding: 14px 20px;
        background: rgba(255, 255, 255, 0.08);
        border: 2px solid rgba(255, 255, 255, 0.15);
        color: white;
        border-radius: 10px;
        cursor: pointer;
        font-weight: 500;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        text-align: center;
      }

      .btn:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      .btn:active {
        transform: translateY(0);
      }

      /* Instructions within sidebar */
      .instructions {
        margin-top: 20px;
      }

      .instructions h4 {
        color: #667eea;
        margin-bottom: 15px;
        font-size: 1.2rem;
        font-weight: 600;
      }

      .instructions ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .instructions li {
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
        font-size: 0.9rem;
        opacity: 0.8;
        line-height: 1.4;
      }

      .instructions li::before {
        content: "→";
        position: absolute;
        left: 0;
        color: #667eea;
        font-weight: bold;
      }

      /* Footer */
      .footer {
        grid-area: footer;
        text-align: center;
        padding: 20px 0;
        opacity: 0.6;
        font-size: 0.9rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: 20px;
      }

      @media (max-width: 1200px) {
        .main-container {
          grid-template-columns: 1fr;
          grid-template-areas:
            "header"
            "animation"
            "sidebar"
            "controls"
            "footer";
          gap: 30px;
        }

        .sidebar,
        .controls-panel {
          max-width: 600px;
          margin: 0 auto;
        }

        .header h1 {
          font-size: 2.5rem;
        }
      }

      @media (max-width: 768px) {
        body {
          padding: 20px 15px;
        }

        .main-container {
          gap: 20px;
        }

        .paper-container {
          width: 480px;
          height: 336px;
        }

        .reveal-area {
          width: 288px;
          height: 192px;
          top: 72px;
          left: 96px;
        }

        .piece-1,
        .piece-2,
        .piece-3,
        .piece-4 {
          width: 144px;
          height: 96px;
        }

        .piece-1 {
          top: 72px;
          left: 96px;
        }
        .piece-2 {
          top: 72px;
          right: 96px;
        }
        .piece-3 {
          bottom: 72px;
          left: 96px;
        }
        .piece-4 {
          bottom: 72px;
          right: 96px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .header p {
          font-size: 1rem;
        }

        .sidebar,
        .controls-panel {
          padding: 20px;
        }
      }

      @media (max-width: 480px) {
        .paper-container {
          width: 360px;
          height: 252px;
        }

        .reveal-area {
          width: 216px;
          height: 144px;
          top: 54px;
          left: 72px;
        }

        .piece-1,
        .piece-2,
        .piece-3,
        .piece-4 {
          width: 108px;
          height: 72px;
        }

        .piece-1 {
          top: 54px;
          left: 72px;
        }
        .piece-2 {
          top: 54px;
          right: 72px;
        }
        .piece-3 {
          bottom: 54px;
          left: 72px;
        }
        .piece-4 {
          bottom: 54px;
          right: 72px;
        }

        .header h1 {
          font-size: 1.8rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="main-container">
      <!-- Header -->
      <div class="header">
        <h1>Organic Paper Fold Animation</h1>
        <p>Watch your images come to life through natural paper unfolding</p>
      </div>

      <!-- Sidebar with Upload -->
      <div class="sidebar">
        <div class="upload-section">
          <h3>📸 Upload Image</h3>
          <label for="imageUpload" class="upload-btn">
            Choose Your Image
          </label>
          <input
            type="file"
            id="imageUpload"
            class="file-input"
            accept="image/*"
          />
          <div class="upload-description">
            Upload an image to see it revealed through organic paper animation.
            Supports JPG, PNG, and other common formats.
          </div>
        </div>

        <div class="texture-upload">
          <h4>🎨 Paper Texture</h4>
          <label for="textureUpload" class="texture-btn">
            Upload Paper Texture
          </label>
          <input
            type="file"
            id="textureUpload"
            class="file-input"
            accept="image/*"
          />
          <div class="texture-description">
            Upload your own paper texture for a custom look. Works best with
            seamless patterns.
          </div>
        </div>

        <div class="instructions">
          <h4>How to Use:</h4>
          <ul>
            <li>Upload your image above</li>
            <li>Optionally add custom paper texture</li>
            <li>Watch organic paper unfold from center</li>
            <li>Use controls on the right</li>
            <li>Press SPACE to pause</li>
            <li>Press R to reset</li>
            <li>Press ENTER to trigger once</li>
          </ul>
        </div>
      </div>

      <!-- Animation Area -->
      <div class="animation-area">
        <div class="paper-container" id="paperContainer">
          <div class="paper-piece reveal-area" id="revealArea"></div>
          <div class="paper-overlay" id="paperOverlay"></div>
        </div>
      </div>

      <!-- Controls Panel -->
      <div class="controls-panel">
        <h3>🎮 Animation Controls</h3>
        <div class="controls">
          <button class="btn" onclick="triggerUnfold()">🎬 Unfold Once</button>
          <button class="btn" onclick="pauseAnimation()">⏸️ Pause</button>
          <button class="btn" onclick="resumeAnimation()">▶️ Resume</button>
          <button class="btn" onclick="resetAnimation()">🔄 Reset</button>
          <button class="btn" onclick="changeSpeed()">⚡ Speed: 1x</button>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <p>
          Drag and drop images anywhere on the page • Keyboard shortcuts
          available
        </p>
      </div>
    </div>

    <script>
      const paperContainer = document.getElementById("paperContainer");
      const revealArea = document.getElementById("revealArea");
      const paperOverlay = document.getElementById("paperOverlay");
      const imageUpload = document.getElementById("imageUpload");
      const textureUpload = document.getElementById("textureUpload");
      let isPaused = false;
      let currentSpeed = 1;
      const speeds = [0.5, 1, 1.5, 2];
      let speedIndex = 1;

      // Image upload functionality with dynamic sizing
      imageUpload.addEventListener("change", function (e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function (e) {
            const img = new Image();
            img.onload = function () {
              resizeAnimationToImage(img.width, img.height);
              revealArea.style.backgroundImage = `url(${e.target.result})`;
              revealArea.style.backgroundSize = "contain";
              revealArea.style.backgroundPosition = "center";
              revealArea.style.backgroundRepeat = "no-repeat";
              // Trigger a single unfold to show the new image
              triggerUnfold();
            };
            img.src = e.target.result;
          };
          reader.readAsDataURL(file);
        }
      });

      // Texture upload functionality
      textureUpload.addEventListener("change", function (e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function (e) {
            // Update the paper overlay texture
            const textureUrl = e.target.result;
            paperOverlay.style.setProperty(
              "--custom-texture",
              `url(${textureUrl})`
            );

            // Update the CSS to use custom texture
            const style = document.createElement("style");
            style.textContent = `
              .paper-overlay::before {
                background-image: url(${textureUrl}) !important;
                background-size: 50px 50px !important;
                opacity: 0.8 !important;
              }
            `;
            document.head.appendChild(style);
          };
          reader.readAsDataURL(file);
        }
      });

      // Function to resize animation based on image dimensions
      function resizeAnimationToImage(imageWidth, imageHeight) {
        const maxWidth = Math.min(800, window.innerWidth * 0.6); // Max 800px or 60% of viewport
        const maxHeight = Math.min(600, window.innerHeight * 0.6); // Max 600px or 60% of viewport
        const minWidth = 400;
        const minHeight = 300;

        // Calculate aspect ratio
        const aspectRatio = imageWidth / imageHeight;

        let containerWidth, containerHeight;
        let revealWidth, revealHeight;

        // Determine container size based on aspect ratio
        if (aspectRatio > 1.5) {
          // Wide image
          containerWidth = Math.min(
            maxWidth,
            Math.max(minWidth, imageWidth * 0.8)
          );
          containerHeight = containerWidth / aspectRatio + 120; // Reduced padding for single overlay
          revealWidth = containerWidth * 0.75;
          revealHeight = revealWidth / aspectRatio;
        } else if (aspectRatio < 0.75) {
          // Tall image
          containerHeight = Math.min(
            maxHeight,
            Math.max(minHeight, imageHeight * 0.8)
          );
          containerWidth = containerHeight * aspectRatio + 160; // Reduced padding for single overlay
          revealHeight = containerHeight * 0.75;
          revealWidth = revealHeight * aspectRatio;
        } else {
          // Square-ish image
          const size = Math.min(
            maxWidth,
            maxHeight,
            Math.max(minWidth, Math.max(imageWidth, imageHeight) * 0.8)
          );
          containerWidth = size;
          containerHeight = size;
          revealWidth = size * 0.75;
          revealHeight = revealWidth / aspectRatio;
        }

        // Apply new dimensions
        paperContainer.style.width = containerWidth + "px";
        paperContainer.style.height = containerHeight + "px";

        // Update reveal area
        revealArea.style.width = revealWidth + "px";
        revealArea.style.height = revealHeight + "px";
        revealArea.style.top = (containerHeight - revealHeight) / 2 + "px";
        revealArea.style.left = (containerWidth - revealWidth) / 2 + "px";

        // Update paper overlay to match container
        paperOverlay.style.width = containerWidth + "px";
        paperOverlay.style.height = containerHeight + "px";
      }

      // Animation control functions
      function triggerUnfold() {
        paperContainer.classList.remove("single-mode");
        paperContainer.classList.add("single-mode");

        // Remove the class after animation completes
        setTimeout(() => {
          paperContainer.classList.remove("single-mode");
        }, 3000);
      }

      function pauseAnimation() {
        isPaused = true;
        paperContainer.classList.add("paused");
      }

      function resumeAnimation() {
        isPaused = false;
        paperContainer.classList.remove("paused");
      }

      function resetAnimation() {
        paperContainer.classList.remove("single-mode", "paused");
        isPaused = false;
      }

      function changeSpeed() {
        speedIndex = (speedIndex + 1) % speeds.length;
        currentSpeed = speeds[speedIndex];

        // Update animation speed
        paperOverlay.style.animationDuration = 4 / currentSpeed + "s";

        // Update button text
        document.querySelector(
          '[onclick="changeSpeed()"]'
        ).textContent = `⚡ Speed: ${currentSpeed}x`;
      }

      // Animation controls
      function triggerUnfold() {
        paperContainer.classList.remove("paused");
        paperContainer.classList.add("single-mode");

        setTimeout(() => {
          paperContainer.classList.remove("single-mode");
        }, 4000); // Slightly longer for organic movement
      }

      function pauseAnimation() {
        paperContainer.classList.add("paused");
        isPaused = true;
      }

      function resumeAnimation() {
        paperContainer.classList.remove("paused");
        isPaused = false;
      }

      function resetAnimation() {
        paperContainer.classList.remove("paused", "single-mode");
        isPaused = false;

        // Force reflow to restart animations
        paperContainer.style.animation = "none";
        paperContainer.offsetHeight;
        paperContainer.style.animation = null;
      }

      function changeSpeed() {
        speedIndex = (speedIndex + 1) % speeds.length;
        currentSpeed = speeds[speedIndex];

        const pieces = document.querySelectorAll(".paper-piece");
        pieces.forEach((piece) => {
          piece.style.animationDuration = `${3 / currentSpeed}s`;
        });

        // Update button text to show current speed
        event.target.textContent = `⚡ ${currentSpeed}x`;
      }

      // Keyboard controls
      document.addEventListener("keydown", (e) => {
        switch (e.key) {
          case " ":
            e.preventDefault();
            if (isPaused) {
              resumeAnimation();
            } else {
              pauseAnimation();
            }
            break;
          case "r":
          case "R":
            resetAnimation();
            break;
          case "Enter":
            triggerUnfold();
            break;
        }
      });

      // Drag and drop functionality
      document.addEventListener("dragover", (e) => {
        e.preventDefault();
        document.body.style.backgroundColor = "#2a2a2a";
      });

      document.addEventListener("dragleave", (e) => {
        if (!e.relatedTarget) {
          document.body.style.background =
            "linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)";
        }
      });

      document.addEventListener("drop", (e) => {
        e.preventDefault();
        document.body.style.background =
          "linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)";

        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith("image/")) {
          const reader = new FileReader();
          reader.onload = function (e) {
            const img = new Image();
            img.onload = function () {
              resizeAnimationToImage(img.width, img.height);
              revealArea.style.backgroundImage = `url(${e.target.result})`;
              revealArea.style.backgroundSize = "contain";
              revealArea.style.backgroundPosition = "center";
              revealArea.style.backgroundRepeat = "no-repeat";
              triggerUnfold();
            };
            img.src = e.target.result;
          };
          reader.readAsDataURL(files[0]);
        }
      });

      // Add some interactive feedback
      document.querySelectorAll(".btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          btn.style.transform = "translateY(0) scale(0.95)";
          setTimeout(() => {
            btn.style.transform = "";
          }, 150);
        });
      });

      // Auto-trigger unfold on page load
      setTimeout(() => {
        triggerUnfold();
      }, 1000);
    </script>
  </body>
</html>
