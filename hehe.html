<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Paper Fold-Out Animation</title>
    <style>
      body {
        margin: 0;
        height: 100vh;
        background: #f4f4f4;
        display: flex;
        align-items: center;
        justify-content: center;
        perspective: 1000px;
      }

      .paper-container {
        width: 300px;
        height: 200px;
        position: relative;
        transform-style: preserve-3d;
        transition: transform 1s ease;
      }

      .panel {
        position: absolute;
        width: 100%;
        height: 100%;
        background: white;
        border: 2px solid #ccc;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: sans-serif;
        font-size: 20px;
        backface-visibility: hidden;
        transform-origin: top;
      }

      .panel.back {
        background: #fffae6;
        transform: rotateX(-90deg);
      }

      .paper-container.unfolded .panel.back {
        transform: rotateX(0deg);
      }

      .paper-container.unfolded .panel.front {
        transform: rotateX(90deg);
      }
    </style>
  </head>
  <body>
    <div class="paper-container" id="paper">
      <div class="panel front">Click to Unfold</div>
      <div class="panel back">Unfolded Content</div>
    </div>

    <script>
      const paper = document.getElementById("paper");
      paper.addEventListener("click", () => {
        paper.classList.toggle("unfolded");
      });
    </script>
  </body>
</html>
