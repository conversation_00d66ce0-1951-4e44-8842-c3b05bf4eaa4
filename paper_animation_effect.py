import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageDraw
import random
import os
import math

# ---- CONFIG ----
image_path = "IMG-20250603-WA0001.png"       # Your subject image
texture_path = "white-paper-texture.jpg"   # Your paper texture
output_video = "paper_unwrap_animation.mp4"
frame_size = (720, 720)
frame_count = 60                     # More frames for smoother animation
fps = 12                             # Slightly faster for more dynamic feel

def create_paper_fold_distortion(width, height, fold_intensity, fold_pattern):
    """Create realistic paper fold distortions with physics-based crumpling"""
    x_map = np.zeros((height, width), dtype=np.float32)
    y_map = np.zeros((height, width), dtype=np.float32)
    
    # Create coordinate grids
    x_coords, y_coords = np.meshgrid(np.arange(width), np.arange(height))
    x_map = x_coords.astype(np.float32)
    y_map = y_coords.astype(np.float32)
    
    # Apply different fold patterns based on unwrapping stage
    if fold_pattern == 'crumpled':
        # Multiple random folds for crumpled ball effect with physics
        for i in range(12):  # More folds for realism
            cx, cy = random.randint(width//4, 3*width//4), random.randint(height//4, 3*height//4)
            angle = random.uniform(0, 2*np.pi)
            
            # Create fold line
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            
            # Distance from fold line with physics-based deformation
            dist_from_fold = ((x_coords - cx) * sin_a - (y_coords - cy) * cos_a)
            
            # Create fold effect with exponential decay
            fold_strength = fold_intensity * np.exp(-np.abs(dist_from_fold) / (40 + i*5)
            fold_displacement = np.sign(dist_from_fold) * fold_strength
            
            # Add secondary perpendicular distortion
            perp_displacement = fold_intensity * 0.2 * np.exp(-np.abs(dist_from_fold) / 60)
            
            x_map += fold_displacement * cos_a * 0.7 + perp_displacement * sin_a * 0.3
            y_map += fold_displacement * sin_a * 0.7 - perp_displacement * cos_a * 0.3
    
    elif fold_pattern == 'partially_unfolded':
        # Directional folds with unfolding physics
        for i in range(4):
            fold_y = height * (0.2 + i * 0.2)
            fold_strength = fold_intensity * (1 - i * 0.25)  # Gradually reduce strength
            
            # Horizontal fold with easing
            dist_from_fold = np.abs(y_coords - fold_y)
            fold_effect = fold_strength * (1 - np.tanh(dist_from_fold / 40))  # Smoother transition
            
            # Alternate fold directions with physics-based bending
            direction = 1 if i % 2 == 0 else -1
            bend_effect = fold_strength * 0.3 * np.sin(x_coords/width * np.pi * 2)  # Wave-like bend
            
            y_map += (fold_effect * direction) + bend_effect
    
    elif fold_pattern == 'mostly_flat':
        # Light corner folds with residual wrinkles
        corners = [(0, 0), (width, 0), (0, height), (width, height)]
        for cx, cy in corners:
            dist = np.sqrt((x_coords - cx)**2 + (y_coords - cy)**2)
            corner_fold = fold_intensity * 0.4 * np.exp(-dist / 120)
            
            # Push corners with directional bias
            if cx == 0:
                x_map += corner_fold * (1 + 0.2*np.sin(y_coords/height*np.pi))
            else:
                x_map -= corner_fold * (1 + 0.2*np.cos(y_coords/height*np.pi))
            if cy == 0:
                y_map += corner_fold * (1 + 0.2*np.cos(x_coords/width*np.pi))
            else:
                y_map -= corner_fold * (1 + 0.2*np.sin(x_coords/width*np.pi))
    
    return x_map, y_map

def create_dynamic_paper_mask(frame_index, stage, stage_progress):
    """Create dynamically changing paper mask with bouncing and unfolding physics"""
    mask = Image.new("L", frame_size, 0)
    draw = ImageDraw.Draw(mask)
    
    center_x, center_y = frame_size[0] // 2, frame_size[1] // 2
    
    # Add bouncing physics to the ball
    if stage == 1:
        bounce_progress = stage_progress * 2  # Speed up bouncing
        bounce_height = 40 * (1 - bounce_progress)  # Reduce bounce height over time
        bounce_cycle = math.sin(bounce_progress * 3 * math.pi)  # 3 bounces
        
        # Vertical position with bounce
        center_y = center_y + bounce_height * bounce_cycle
        
        # Ball grows while bouncing
        base_radius = 50 + stage_progress * 40
        
        # Create irregular ball outline with physics-based deformation
        points = []
        for angle in range(0, 360, 10):
            rad = math.radians(angle)
            # Physics-based deformation from impacts
            radius_var = base_radius * (1 + 0.2 * math.sin(rad * 3 + frame_index * 0.2))
            radius_var += random.randint(-10, 10)  # Random surface variation
            
            # Add squash/stretch based on bounce phase
            squash = 1.0 - 0.3 * abs(bounce_cycle)  # Squash at impact
            stretch = 1.0 + 0.2 * abs(bounce_cycle)  # Stretch in air
            
            x = center_x + math.cos(rad) * radius_var * stretch
            y = center_y + math.sin(rad) * radius_var * squash
            points.append((x, y))
        
        # Fill the ball area
        draw.polygon(points, fill=255)
        
    elif stage == 2:  # Physics-based unfolding
        unfold_progress = stage_progress
        
        # Expand with easing and physics
        expand_width = 80 + unfold_progress**2 * 300  # Ease-out expansion
        expand_height = 70 + unfold_progress**1.5 * 250  # Different expansion rates
        
        # Create organic unfolding shape with physics
        points = []
        for angle in range(0, 360, 8):
            rad = math.radians(angle)
            
            # Physics-based unfolding - different expansion in different directions
            if -60 <= math.degrees(rad) <= 60:
                # Horizontal expansion (faster)
                radius = expand_width * (0.9 + 0.1 * math.sin(frame_index * 0.2))
            elif 120 <= math.degrees(rad) <= 240:
                # Vertical expansion (slower)
                radius = expand_height * (0.8 + 0.2 * math.cos(frame_index * 0.3))
            else:
                # Diagonal expansion (medium)
                radius = (expand_width + expand_height) / 2
            
            # Add unfolding jitter
            radius += random.randint(-15, 15) * (1 - unfold_progress)
            
            x = center_x + math.cos(rad) * radius
            y = center_y + math.sin(rad) * radius
            points.append((x, y))
        
        draw.polygon(points, fill=255)
        
    elif stage == 3:  # Physics-based flattening
        margin = 90 - stage_progress * 60
        
        # Create paper with physics-based edge curling
        points = []
        
        # Top edge with curl physics
        curl_strength = 20 * (1 - stage_progress)
        for x in range(int(margin), int(frame_size[0] - margin), 20):
            curl = curl_strength * math.sin(x / frame_size[0] * math.pi * 4)
            y = margin + random.randint(-8, 8) + curl
            points.append((x, y))
        
        # Right edge with physics-based deformation
        right_x = frame_size[0] - margin
        for y in range(int(margin), int(frame_size[1] - margin), 25):
            deform = 10 * math.cos(y / frame_size[1] * math.pi * 3)
            x = right_x + random.randint(-10, 5) + deform
            points.append((x, y))
        
        # Bottom edge with physics-based settling
        for x in range(int(frame_size[0] - margin), int(margin), -20):
            settle = 8 * math.sin(x / frame_size[0] * math.pi * 2)
            y = frame_size[1] - margin + random.randint(-8, 8) - abs(settle)
            points.append((x, y))
        
        # Left edge with residual wrinkles
        for y in range(int(frame_size[1] - margin), int(margin), -25):
            wrinkle = 12 * (1 - stage_progress) * math.sin(y / 50)
            x = margin + random.randint(-5, 10) + wrinkle
            points.append((x, y))
        
        draw.polygon(points, fill=255)
        
    else:  # Stage 4: Fully unfolded
        mask = Image.new("L", frame_size, 255)
    
    return mask

def get_animation_stage(frame_index, total_frames):
    """Get current animation stage and progress with physics-based timing"""
    progress = frame_index / (total_frames - 1)
    
    # Physics-based timing with easing
    if progress < 0.25:  # Longer bouncing stage
        return 1, (progress / 0.25)  # Stage 1: Bouncing ball (25%)
    elif progress < 0.45:  # Quick unfolding
        return 2, ((progress - 0.25) / 0.2)  # Stage 2: Initial unfolding (20%)
    elif progress < 0.75:  # Slower flattening
        return 3, ((progress - 0.45) / 0.3)  # Stage 3: Flattening (30%)
    else:
        return 4, ((progress - 0.75) / 0.25)  # Stage 4: Final settle (25%)

def smooth_ease_out(t):
    """Physics-based easing for natural motion"""
    return 1 - (1 - t) ** 4

def bounce_ease(t):
    """Physics-based bounce easing function"""
    if t < 0.5:
        return 2 * t * t
    else:
        return 1 - (2 * (1 - t) * (1 - t))

# Load and prepare assets
texture = Image.open(texture_path).convert("L")
texture = texture.resize(frame_size)
texture = ImageEnhance.Contrast(texture).enhance(2.0)  # Higher contrast
texture = texture.filter(ImageFilter.GaussianBlur(0.5))
texture_np = np.array(texture)
texture_np = cv2.cvtColor(texture_np, cv2.COLOR_GRAY2BGR)

original = Image.open(image_path).convert("RGBA")
original = original.resize(frame_size)

green_bg = Image.new("RGBA", frame_size, (0, 255, 0, 255))

# Video writer
fourcc = cv2.VideoWriter_fourcc(*'mp4v')
out = cv2.VideoWriter(output_video, fourcc, fps, frame_size)

print("Generating physics-based paper unwrapping animation...")
print("This will create a realistic paper unfolding effect with:")
print("- Physics-based crumpling and unfolding")
print("- Dynamic bouncing ball introduction")
print("- Progressive image revelation with organic unfolding")
print("- Enhanced paper texture effects")

for i in range(frame_count):
    stage, stage_progress = get_animation_stage(i, frame_count)
    
    # Apply physics-based easing
    if stage == 1:
        smooth_progress = bounce_ease(stage_progress)  # Bounce easing for ball
    else:
        smooth_progress = smooth_ease_out(stage_progress)  # Smooth easing for unfolding
    
    # Set random seed for this frame (for stop-motion variation)
    random.seed(42 + i)
    
    # Create paper mask for this frame
    paper_mask = create_dynamic_paper_mask(i, stage, smooth_progress)
    
    # Start with green background + original image
    base_frame = Image.alpha_composite(green_bg, original)
    frame_array = np.array(base_frame.convert("RGB"))
    
    # Apply paper folding distortion with physics-based parameters
    if stage == 1:
        fold_intensity = 40 - smooth_progress * 15  # Stronger crumpling
        fold_pattern = 'crumpled'
    elif stage == 2:
        fold_intensity = 25 - smooth_progress * 20
        fold_pattern = 'partially_unfolded'
    elif stage == 3:
        fold_intensity = 8 - smooth_progress * 6
        fold_pattern = 'mostly_flat'
    else:
        fold_intensity = 1 - smooth_progress
        fold_pattern = 'mostly_flat'
    
    # Apply distortion if significant
    if fold_intensity > 0.5:
        x_map, y_map = create_paper_fold_distortion(
            frame_size[0], frame_size[1], fold_intensity, fold_pattern
        )
        frame_array = cv2.remap(frame_array, x_map, y_map, cv2.INTER_LINEAR, 
                               borderMode=cv2.BORDER_REFLECT)
    
    # Apply paper mask
    mask_array = np.array(paper_mask) / 255.0
    mask_3d = np.stack([mask_array, mask_array, mask_array], axis=2)
    
    # Create white paper background
    paper_color = [245, 245, 245]  # Slightly off-white
    paper_bg = np.full_like(frame_array, paper_color)
    
    # Blend image with paper background based on mask
    frame_array = frame_array * mask_3d + paper_bg * (1 - mask_3d)
    
    # Add paper texture (stronger in early stages)
    texture_strength = 0.3 * (1 - (stage - 1) / 3) + 0.1
    frame_array = cv2.addWeighted(frame_array.astype(np.uint8), 0.8, 
                                 texture_np, texture_strength, 0)
    
    # Enhanced stop-motion effects with physics-based parameters
    jitter_amount = max(2, int(8 - stage))  # More jitter in early stages
    dx = random.randint(-jitter_amount, jitter_amount)
    dy = random.randint(-jitter_amount, jitter_amount)
    
    if abs(dx) > 0 or abs(dy) > 0:
        M = np.float32([[1, 0, dx], [0, 1, dy]])
        frame_array = cv2.warpAffine(frame_array, M, frame_size, 
                                    borderValue=(0, 255, 0))
    
    # Physics-based lighting variation
    brightness_var = random.uniform(0.95, 1.05)
    contrast_var = random.uniform(0.97, 1.03)
    frame_array = np.clip(frame_array * brightness_var, 0, 255).astype(np.uint8)
    frame_array = np.clip(128 + contrast_var * (frame_array - 128), 0, 255).astype(np.uint8)
    
    # Add subtle motion blur for stop-motion effect
    if stage < 3 and random.random() > 0.3:
        kernel_size = random.choice([3, 5])
        frame_array = cv2.GaussianBlur(frame_array, (kernel_size, kernel_size), 0)
    
    out.write(frame_array)
    
    # Physics-based progress updates
    if (i + 1) % 10 == 0 or i == frame_count - 1:
        stage_names = {1: "Bouncing Ball", 2: "Unfolding", 3: "Flattening", 4: "Final Settle"}
        print(f"Frame {i + 1}/{frame_count} - {stage_names[stage]} - Progress: {i/frame_count:.1%}")

out.release()
print(f"✅ Physics-based paper unwrapping animation saved to {output_video}")
print(f"📹 {frame_count} frames at {fps} fps = {frame_count/fps:.1f} seconds")
print("🎬 Features enhanced physics-based crumpling and organic unfolding!")